import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../models/erp_category.dart';
import '../../../utils/category_icons.dart';
import '../../../../colors.dart';
import '../controllers/categories_controller.dart';

class CategoriesView extends GetView<CategoriesController> {
  const CategoriesView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ErpColors.backgroundLight,
      body: controller.obx(
        (categories) => _buildContent(context, categories ?? []),
        onLoading: const Center(
          child: CircularProgressIndicator(),
        ),
        onError: (error) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: ErpColors.error,
              ),
              const SizedBox(height: 16),
              Text(
                '載入失敗',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: ErpColors.textPrimary,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: TextStyle(
                  color: ErpColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: controller.refreshCategories,
                child: const Text('重試'),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildContent(BuildContext context, List<ErpCategory> categories) {
    return RefreshIndicator(
      onRefresh: () async {
        controller.refreshCategories();
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const SizedBox(height: 8),
            _buildCategoriesList(categories),
            const SizedBox(height: 24),
            _buildStatisticsCard(),
            const SizedBox(height: 100), // Space for FAB
          ],
        ),
      ),
    );
  }

  Widget _buildCategoriesList(List<ErpCategory> categories) {
    if (categories.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: categories.map((category) => _buildCategoryItem(category)).toList(),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.category_outlined,
            size: 64,
            color: ErpColors.textHint,
          ),
          const SizedBox(height: 16),
          Text(
            '尚無分類',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: ErpColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '點擊右下角的 + 按鈕新增第一個分類',
            style: TextStyle(
              color: ErpColors.textHint,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryItem(ErpCategory category) {
    final categoryName = category.name ?? '未知分類';
    final transactionCount = category.children.length;
    final amount = category.amount ?? 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: ErpColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        elevation: 2,
        shadowColor: ErpColors.shadow,
        child: InkWell(
          onTap: () => controller.navigateToCategoryDetail(category),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                _buildCategoryIcon(categoryName),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildCategoryInfo(categoryName, transactionCount, amount),
                ),
                Icon(
                  Icons.chevron_right,
                  color: ErpColors.textHint,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryIcon(String categoryName) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: CategoryIcons.getBackgroundColor(categoryName),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(
        CategoryIcons.getIcon(categoryName),
        color: CategoryIcons.getColor(categoryName),
        size: 20,
      ),
    );
  }

  Widget _buildCategoryInfo(String categoryName, int transactionCount, num amount) {
    final isIncome = amount > 0;
    final amountText = amount == 0
        ? '\$0'
        : '${isIncome ? '+' : '-'}\$${amount.abs().toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          categoryName,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: ErpColors.textPrimary,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '$transactionCount 筆交易 • 本月 $amountText',
          style: const TextStyle(
            fontSize: 12,
            color: ErpColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildStatisticsCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ErpColors.backgroundDark,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '統計資訊',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ErpColors.textPrimary,
            ),
          ),
          const SizedBox(height: 12),
          Obx(() => _buildStatisticsGrid()),
        ],
      ),
    );
  }

  Widget _buildStatisticsGrid() {
    return Row(
      children: [
        Expanded(
          child: _buildStatItem(
            '總類別數',
            '${controller.totalCategories.value} 個',
          ),
        ),
        Expanded(
          child: _buildStatItem(
            '有交易類別',
            '${controller.categoriesWithTransactions.value} 個',
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: ErpColors.textSecondary,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: ErpColors.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildFloatingActionButton() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(28),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            ErpColors.gradientStart,
            ErpColors.gradientEnd,
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: ErpColors.gradientStart.withOpacity(0.4),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: FloatingActionButton(
        onPressed: controller.showAddCategoryDialog,
        backgroundColor: Colors.transparent,
        elevation: 0,
        child: const Icon(
          Icons.add,
          color: ErpColors.textWhite,
          size: 24,
        ),
      ),
    );
  }
}
