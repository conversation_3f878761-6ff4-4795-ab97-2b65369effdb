import 'package:get/get.dart';
import '../../../models/erp_category.dart';
import '../../../repositories/category_repository.dart';
import '../../../providers/box_provider.dart';
import '../../../routes/app_pages.dart';

class CategoriesController extends GetxController with StateMixin<List<ErpCategory>> {
  late final CategoryRepository _categoryRepository;

  // Observable variables
  final categories = <ErpCategory>[].obs;
  final totalCategories = 0.obs;
  final categoriesWithTransactions = 0.obs;
  final mostUsedCategory = ''.obs;
  final totalTransactions = 0.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeRepository();
    _loadCategories();
  }

  void _initializeRepository() {
    final boxProvider = Get.find<BoxProvider>();
    _categoryRepository = CategoryRepository(boxProvider.store);
  }

  void _loadCategories() {
    try {
      change(null, status: RxStatus.loading());

      final categoryList = _categoryRepository.getAllSorted();

      // If no categories exist, create sample data
      if (categoryList.isEmpty) {
        _createSampleCategories();
        final updatedList = _categoryRepository.getAllSorted();
        categories.value = updatedList;
        _calculateStatistics(updatedList);
        change(updatedList, status: RxStatus.success());
      } else {
        categories.value = categoryList;
        _calculateStatistics(categoryList);
        change(categoryList, status: RxStatus.success());
      }
    } catch (e) {
      change(null, status: RxStatus.error('Failed to load categories: $e'));
    }
  }

  void _createSampleCategories() {
    final sampleCategories = [
      ErpCategory(
        name: '薪水',
        color: '#10B981',
        sort: 1,
      ),
      ErpCategory(
        name: '餐飲',
        color: '#EF4444',
        sort: 2,
      ),
      ErpCategory(
        name: '購物',
        color: '#8B5CF6',
        sort: 3,
      ),
      ErpCategory(
        name: '交通',
        color: '#3B82F6',
        sort: 4,
      ),
      ErpCategory(
        name: '娛樂',
        color: '#F59E0B',
        sort: 5,
      ),
      ErpCategory(
        name: '獎金',
        color: '#F59E0B',
        sort: 6,
      ),
      ErpCategory(
        name: '居家',
        color: '#6B7280',
        sort: 7,
      ),
      ErpCategory(
        name: '副業',
        color: '#7C3AED',
        sort: 8,
      ),
    ];

    _categoryRepository.putMany(sampleCategories);
  }

  void _calculateStatistics(List<ErpCategory> categoryList) {
    totalCategories.value = categoryList.length;

    // Count categories with transactions
    int withTransactions = 0;
    String mostUsed = '';
    int maxTransactions = 0;
    int totalTrans = 0;

    for (final category in categoryList) {
      final transactionCount = category.children.length;
      totalTrans += transactionCount;

      if (transactionCount > 0) {
        withTransactions++;

        if (transactionCount > maxTransactions) {
          maxTransactions = transactionCount;
          mostUsed = category.name ?? '';
        }
      }
    }

    categoriesWithTransactions.value = withTransactions;
    mostUsedCategory.value = mostUsed.isEmpty ? '無' : mostUsed;
    totalTransactions.value = totalTrans;
  }

  void navigateToCategoryDetail(ErpCategory category) {
    Get.toNamed(Routes.CATEGORY_DETAIL, arguments: category);
  }

  void showAddCategoryDialog() {
    Get.toNamed(Routes.CATEGORY_DETAIL);
  }

  void refreshCategories() {
    _loadCategories();
  }
}
